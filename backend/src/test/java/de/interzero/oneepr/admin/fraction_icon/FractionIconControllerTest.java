package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.fraction_icon.dto.CreateFractionIconDto;
import de.interzero.oneepr.common.string.Api;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link FractionIconController}.
 * This class validates the full HTTP request-response cycle for the fraction icon module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class FractionIconControllerTest {

    private static final String API_URL = Api.FRACTION_ICONS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private FilesRepository filesRepository;

    @Autowired
    private FractionIconRepository fractionIconRepository;

    private FractionIcon testFractionIcon;

    /**
     * Sets up a consistent database state before each test method runs.
     * This data is automatically rolled back by the @Transactional annotation after each test.
     */
    @BeforeEach
    void setUp() {
        Files file = createAndSaveTestFile("test-icon.png");
        testFractionIcon = createAndSaveTestFractionIcon(file);
    }

    /**
     * Verifies that a POST request to {@link FractionIconController#create(CreateFractionIconDto)}
     * successfully creates a new fraction icon.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void create_shouldCreateNewFractionIcon() throws Exception {
        Files newFile = createAndSaveTestFile("new-icon.svg");
        CreateFractionIconDto createDto = getCreateFractionIconDto(newFile.getId());

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.file_id", is(newFile.getId())))
                .andExpect(jsonPath("$.image_url").value(
                        "new-icon.svg"));
    }

    /**
     * Verifies that a GET request to {@link FractionIconController#findAll()} returns a list of all active fraction icons.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findAll_shouldReturnListOfFractionIcons() throws Exception {
        mockMvc.perform(get(API_URL))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testFractionIcon.getId())));
    }

    /**
     * Verifies that a GET request to {@link FractionIconController#findOne(Integer)} returns the correct fraction icon.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnCorrectFractionIcon() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", testFractionIcon.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testFractionIcon.getId())))
                .andExpect(jsonPath("$.image_url", is("https://example.com/test-icon.png")));
    }

    /**
     * Verifies that a DELETE request to {@link FractionIconController#remove(Integer)} successfully soft-deletes a fraction icon.
     */
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void remove_shouldSoftDeleteFractionIcon() throws Exception {
        mockMvc.perform(delete(API_URL + "/{id}", testFractionIcon.getId())).andExpect(status().isNoContent());

        // Verify it's gone from the "active" list
        mockMvc.perform(get(API_URL + "/{id}", testFractionIcon.getId())).andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is set
        FractionIcon deletedIcon = fractionIconRepository.findById(testFractionIcon.getId()).orElseThrow();
        assertNotNull(deletedIcon.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    /**
     * Helper method to create and return a valid {@link CreateFractionIconDto} for testing.
     *
     * @param fileId The ID of the file to associate with the icon.
     * @return A non-null, populated instance of {@code CreateFractionIconDto}.
     */
    private @NotNull CreateFractionIconDto getCreateFractionIconDto(String fileId) {
        return new CreateFractionIconDto(fileId);
    }

    /**
     * Creates and persists a valid {@link Files} entity for test setup.
     *
     * @param name The name for the new file.
     * @return The persisted Files entity.
     */
    private Files createAndSaveTestFile(@NotNull String name) {
        Files file = new Files();
        file.setId(UUID.randomUUID().toString());
        file.setName(name);
        file.setOriginalName(name);
        file.setExtension(name.substring(name.lastIndexOf('.') + 1));
        file.setSize("1024");
        file.setCreatorType("SYSTEM");
        file.setDocumentType("IMAGE");
        file.setUserId("test-user");
        file.setCreatedAt(Instant.now());
        file.setUpdatedAt(Instant.now());
        return filesRepository.saveAndFlush(file);
    }

    /**
     * Creates and persists a valid {@link FractionIcon} entity for test setup.
     *
     * @param file The {@link Files} entity to associate with this icon.
     * @return The persisted FractionIcon entity.
     */
    private FractionIcon createAndSaveTestFractionIcon(@NotNull Files file) {
        FractionIcon icon = new FractionIcon();
        icon.setFile(file);
        icon.setImageUrl("https://example.com/test-icon.png");
        icon.setCreatedAt(Instant.now());
        icon.setUpdatedAt(Instant.now());
        return fractionIconRepository.saveAndFlush(icon);
    }
}
