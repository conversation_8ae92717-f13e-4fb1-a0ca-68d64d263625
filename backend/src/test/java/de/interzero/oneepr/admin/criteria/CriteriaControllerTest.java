package de.interzero.oneepr.admin.criteria;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.dto.CreateCriteriaDto;
import de.interzero.oneepr.admin.criteria.dto.UpdateCriteriaDto;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSection;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSectionRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.common.string.Api;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collections;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Integration tests for the {@link CriteriaController}.
 * This class validates the full HTTP request-response cycle, including security,
 * data validation, and database interactions for the Criteria module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CriteriaControllerTest {

    private static final String API_URL = Api.CRITERIAS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CriteriaRepository criteriaRepository;

    @Autowired
    private CriteriaOptionRepository criteriaOptionRepository;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private ObligationCheckSectionRepository obligationCheckSectionRepository;

    private Country testCountry;

    private ObligationCheckSection testSection;
    private Criteria testCriteria;

    /**
     * Sets up a consistent database state before each test runs by creating
     * all necessary prerequisite entities, such as Country, ObligationCheckSection, and a base Criteria.
     * This data is automatically rolled back by the {@code @Transactional} annotation after each test.
     */
    @BeforeEach
    void setUp() {
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        testCountry = createAndSaveTestCountry();
        testSection = createAndSaveTestSection(testCountry);
        PackagingService testPackagingService = createAndSaveTestPackagingService(testCountry);

        testCriteria = new Criteria();
        testCriteria.setCountry(testCountry);
        testCriteria.setObligationCheckSection(testSection); // Link to section
        testCriteria.setPackagingService(testPackagingService);
        testCriteria.setMode(Criteria.Mode.COMMITMENT);
        testCriteria.setType(Criteria.Type.PACKAGING_SERVICE);
        testCriteria.setTitle("Select a Packaging Service");
        testCriteria.setInputType(Criteria.InputType.RADIO);
        testCriteria.setCreatedAt(Instant.now());
        testCriteria.setUpdatedAt(Instant.now());
        testCriteria = criteriaRepository.save(testCriteria);

        CriteriaOption option = new CriteriaOption();
        option.setCriteria(testCriteria);
        option.setOptionValue("Option A");
        option.setValue("Value A");
        option.setCreatedAt(Instant.now());
        option.setUpdatedAt(Instant.now());
        criteriaOptionRepository.save(option);
    }

    /**
     * Verifies that a POST request with a valid DTO successfully creates a new Criteria and its associated options.
     * The DTO now includes a reference to an ObligationCheckSection.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewCriteriaAndOptions() throws Exception {
        CreateCriteriaDto createDto = getCreateCriteriaDto();

        mockMvc.perform(post(API_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.title", is("Use Authorized Representative?")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())))
                .andExpect(jsonPath(
                        "$.obligation_check_section_id",
                        is(testSection.getId().intValue()))); // Assert section linkage
    }

    /**
     * Verifies that a GET request returns only the criteria associated with a specific section ID.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAllWithSectionId_shouldReturnOnlyCriteriaForThatSection() throws Exception {
        // Arrange: Create a second criteria NOT in the section to ensure filtering works.
        Criteria otherCriteria = new Criteria();
        otherCriteria.setCountry(testCountry);
        otherCriteria.setMode(Criteria.Mode.COMMITMENT);
        otherCriteria.setType(Criteria.Type.REPORT_SET);
        otherCriteria.setTitle("Unrelated Criteria Without Section");
        criteriaRepository.save(otherCriteria);

        // Act & Assert
        mockMvc.perform(get(API_URL + "/section/{id}", testSection.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1))) // Should only find the one from setUp
                .andExpect(jsonPath("$[0].id", is(testCriteria.getId())))
                .andExpect(jsonPath("$[0].title", is("Select a Packaging Service")));
    }


    /**
     * Helper method to create and return a valid {@link CreateCriteriaDto} for testing the creation endpoint.
     * <p>
     * The DTO is pre-populated with standard test data for a "YES_NO" calculator-style criteria.
     *
     * @return A non-null, fully populated instance of {@code CreateCriteriaDto}.
     */
    private @NotNull CreateCriteriaDto getCreateCriteriaDto() {
        CreateCriteriaDto.CriteriaOptionDto optionDto = new CreateCriteriaDto.CriteriaOptionDto();
        optionDto.setOptionValue("Yes");
        optionDto.setValue("1");

        CreateCriteriaDto createDto = new CreateCriteriaDto();
        createDto.setCountryId(testCountry.getId());
        createDto.setMode(Criteria.Mode.CALCULATOR);
        createDto.setObligationCheckSectionId(testSection.getId());
        createDto.setType(Criteria.Type.AUTHORIZE_REPRESENTATIVE);
        createDto.setTitle("Use Authorized Representative?");
        createDto.setInputType(Criteria.InputType.YES_NO);
        createDto.setOptions(Collections.singletonList(optionDto));
        return createDto;
    }

    /**
     * Verifies that a GET request returns only the criteria associated with a specific section ID.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findAll_shouldReturnAllActiveCriteria() throws Exception {
        mockMvc.perform(get(API_URL)).andExpect(status().isOk()).andExpect(jsonPath("$", hasSize(1)));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct Criteria entity.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnCorrectCriteria_whenFound() throws Exception {
        mockMvc.perform(get(API_URL + "/{id}", testCriteria.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCriteria.getId())))
                .andExpect(jsonPath("$.title", is("Select a Packaging Service")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));
    }

    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingCriteria() throws Exception {
        UpdateCriteriaDto updateDto = new UpdateCriteriaDto();
        updateDto.setTitle("New Updated Title");
        updateDto.setHelpText("This is new help text.");
        updateDto.setCountryId(testCountry.getId());
        mockMvc.perform(put(API_URL + "/{id}", testCriteria.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.title", is("New Updated Title")))
                .andExpect(jsonPath("$.help_text", is("This is new help text.")));
    }

    /**
     * Verifies that a PUT request with a new set of options correctly replaces the old options.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldReplaceOptions_whenProvided() throws Exception {
        UpdateCriteriaDto updateDto = getUpdateCriteriaDto();

        mockMvc.perform(put(API_URL + "/{id}", testCriteria.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isOk());

        Criteria updatedCriteria = criteriaRepository.findById(testCriteria.getId()).orElseThrow();
        List<CriteriaOption> options = updatedCriteria.getOptions();

        assertTrue(options.stream().anyMatch(o -> o.getOptionValue().equals("New Option 1")));
    }

    /**
     * Helper method to create a valid {@link UpdateCriteriaDto} for testing the replacement of nested options.
     * <p>
     * The returned DTO contains a new list of {@link UpdateCriteriaDto.UpdateCriteriaOptionDto}
     * intended to replace any existing options on a {@link Criteria} entity during an update operation.
     *
     * @return A non-null, populated DTO for testing the options update logic.
     */
    private @NotNull UpdateCriteriaDto getUpdateCriteriaDto() {
        UpdateCriteriaDto.UpdateCriteriaOptionDto newOption1 = new UpdateCriteriaDto.UpdateCriteriaOptionDto();
        newOption1.setOptionValue("New Option 1");
        newOption1.setValue("Val1");

        UpdateCriteriaDto.UpdateCriteriaOptionDto newOption2 = new UpdateCriteriaDto.UpdateCriteriaOptionDto();
        newOption2.setOptionValue("New Option 2");
        newOption2.setValue("Val2");

        UpdateCriteriaDto updateDto = new UpdateCriteriaDto();
        updateDto.setOptions(List.of(newOption1, newOption2));
        return updateDto;
    }

    /**
     * Verifies that a DELETE request successfully soft-deletes a Criteria record.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void remove_shouldSoftDeleteCriteria() throws Exception {
        mockMvc.perform(delete(API_URL + "/{id}", testCriteria.getId())).andExpect(status().isOk());

        // Verify it's gone from the "active" list
        mockMvc.perform(get(API_URL + "/{id}", testCriteria.getId())).andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is set
        Criteria deletedCriteria = criteriaRepository.findById(testCriteria.getId()).orElseThrow();
        assertNotNull(deletedCriteria.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    /**
     * Creates and persists a valid {@link Country} entity for test setup.
     *
     * @return The persisted Country entity.
     */
    private Country createAndSaveTestCountry() {
        Country country = countryRepository.findByCode("DE").orElse(new Country());
        country.setName("Germany");
        country.setCode("DE");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        country.setUpdatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    /**
     * Creates and persists a valid {@link PackagingService} entity for test setup.
     *
     * @param country The Country to associate with this service.
     * @return The persisted PackagingService entity.
     */
    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Standard Service");
        service.setDescription("Standard packaging service description.");
        service.setCountry(country);
        service.setCreatedAt(Instant.now());
        service.setUpdatedAt(Instant.now());
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ObligationCheckSection createAndSaveTestSection(Country country) {
        ObligationCheckSection section = new ObligationCheckSection();
        section.setTitle("Test Section");
        section.setDisplayOrder(1);
        section.setCountry(country);
        section.setCreatedAt(Instant.now());
        section.setUpdatedAt(Instant.now());
        return obligationCheckSectionRepository.saveAndFlush(section);
    }
}