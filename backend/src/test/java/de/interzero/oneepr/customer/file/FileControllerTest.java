package de.interzero.oneepr.customer.file;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.service.AwsService;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.file.dto.RequestPresignedUrlDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.ByteArrayInputStream;
import java.net.URI;
import java.time.Instant;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class FileControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileRepository fileRepository;

    @MockBean
    private AwsService awsService;

    @Autowired
    private ObjectMapper objectMapper;

    private File testFile;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // Override AWS S3 configuration for tests
        registry.add("aws.region", () -> "eu-west-2");
        registry.add("aws.s3.bucket-name", () -> "test-bucket");
    }

    @BeforeEach
    @Transactional
    void setUp() {
        // Setup test file
        testFile = new File();
        testFile.setId("test-file-id");
        testFile.setName("CONTRACT/2024/07/07/ABC12345-test-file.pdf");
        testFile.setOriginalName("test-file.pdf");
        testFile.setExtension("application/pdf");
        testFile.setSize("1024");
        testFile.setType(File.Type.CONTRACT);
        testFile.setUserId("test-user-id");
        testFile.setCreatedAt(Instant.now());
        testFile.setUpdatedAt(Instant.now());

        // Setup default AWS mocks
        setupDefaultAwsMocks();
    }

    private void setupDefaultAwsMocks() {
        try {
            // Default AwsService mock for presigned URL
            AwsService.PresignedUrlResponse mockPresignedResponse = new AwsService.PresignedUrlResponse(
                    "https://test-bucket.s3.amazonaws.com/upload",
                    new java.util.HashMap<>()
            );
            when(awsService.generatePresignedUploadUrl(any(String.class), any(String.class)))
                    .thenReturn(mockPresignedResponse);

            // Default AwsService mock for uploadFile
            when(awsService.uploadFile(any(String.class), any(org.springframework.web.multipart.MultipartFile.class), any(String.class)))
                    .thenReturn("test-key");

            // Default AwsService mock for downloadFile
            byte[] defaultFileContent = "test file content".getBytes();
            AwsService.FileDownloadResponse mockDownloadResponse = new AwsService.FileDownloadResponse(
                    defaultFileContent, "application/pdf", defaultFileContent.length);
            when(awsService.downloadFile(any(String.class))).thenReturn(mockDownloadResponse);

            when(awsService.getBucketName()).thenReturn("test-bucket");
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup AWS mocks", e);
        }
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileUsingRBAC_ShouldReturnFile() throws Exception {
        // Mock file repository to return test file
        when(fileRepository.findById("test-file-id")).thenReturn(Optional.of(testFile));

        byte[] fileContent = "test file content".getBytes();

        mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/test-file-id"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                .andExpect(MockMvcResultMatchers.header()
                                   .string("Content-Disposition", "attachment; filename=\"test-file.pdf\""))
                .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void requestUrl_ShouldReturnPresignedUrl() throws Exception {
        RequestPresignedUrlDto requestDto = new RequestPresignedUrlDto();
        requestDto.setFilename("test.pdf");
        requestDto.setFileType("application/pdf");

        mockMvc.perform(MockMvcRequestBuilders.post("/customer/files/request-presigned-url")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.uploadUrl")
                                   .value("https://test-bucket.s3.amazonaws.com/upload"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.fields").exists());
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void create_ShouldUploadFile() throws Exception {
        CreateFileDto createDto = new CreateFileDto();
        createDto.setType(File.Type.CONTRACT);
        createDto.setCertificateId(1);
        createDto.setContractId(1);
        createDto.setLicenseId(1);
        createDto.setRequiredInformationId(1);
        createDto.setTerminationId(1);

        MockMultipartFile file = new MockMultipartFile(
                "file",
                "test.pdf",
                "application/pdf",
                "test content".getBytes());

        MockMultipartFile data = new MockMultipartFile(
                "data",
                "",
                "application/json",
                objectMapper.writeValueAsString(createDto).getBytes());

        // Mock file repository save
        when(fileRepository.save(ArgumentMatchers.any())).thenReturn(testFile);

        mockMvc.perform(MockMvcRequestBuilders.multipart("/customer/files")
                                .file(file)
                                .file(data)
                                .header("x-user-id", "test-user-id")
                                .header("x-user-role", "customer"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.id").value("test-file-id"))
                .andExpect(MockMvcResultMatchers.jsonPath("$.type").value("CONTRACT"));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void delete_ShouldDeleteFile() throws Exception {
        // Mock file repository to return test file for deletion
        when(fileRepository.findById("test-file-id")).thenReturn(Optional.of(testFile));
        when(fileRepository.save(ArgumentMatchers.any())).thenReturn(testFile);

        mockMvc.perform(MockMvcRequestBuilders.delete("/customer/files/test-file-id"))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileByRelative_ShouldReturnFile_WhenValidRelationAndId() throws Exception {
        // Mock file repository to return test file by relation
        when(fileRepository.findByRelation("contract_id", 123)).thenReturn(Optional.of(testFile));

        byte[] fileContent = "test file content".getBytes();
        when(awsService.downloadFileContent(any())).thenReturn(fileContent);
        mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/relation")
                                .param("relation", "contract_id")
                                .param("id", "123"))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                .andExpect(MockMvcResultMatchers.header()
                                   .string("Content-Disposition", "attachment; filename=\"test-file.pdf\""))
                .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
    }

    @Test
    @WithMockUser(roles = {TestRole.SUPER_ADMIN})
    void getFileByRelative_ShouldTestAllValidRelations() throws Exception {
        String[] validRelations = {
                "required_information_id",
                "contract_id",
                "certificate_id",
                "license_id",
                "termination_id",
                "general_information_id",
                "third_party_invoice_id",
                "marketing_material_id",
                "partner_contract_id",
                "order_id"
        };

        for (String relation : validRelations) {
            // Mock file repository to return test file for each relation
            when(fileRepository.findByRelation(relation, 123)).thenReturn(Optional.of(testFile));

            byte[] fileContent = "test file content".getBytes();
            when(awsService.downloadFileContent(any())).thenReturn(fileContent);
            mockMvc.perform(MockMvcRequestBuilders.get("/customer/files/relation")
                                    .param("relation", relation)
                                    .param("id", "123"))
                    .andExpect(MockMvcResultMatchers.status().isOk())
                    .andExpect(MockMvcResultMatchers.header().string("Content-Type", "application/pdf"))
                    .andExpect(MockMvcResultMatchers.content().bytes(fileContent));
        }
    }

}
