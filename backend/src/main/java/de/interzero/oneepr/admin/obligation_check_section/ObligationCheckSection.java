package de.interzero.oneepr.admin.obligation_check_section;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.criteria.Criteria;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a section within a country's obligation check.
 * A section acts as a container or a tab to group related obligation questions (Criteria).
 * This entity is derived from the user story for the "Set up Obligation Check" feature.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "obligation_check_section")
public class ObligationCheckSection {

    /**
     * The unique identifier for the section.
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonProperty("id")
    private Long id;

    /**
     * The title of the section, typically displayed as a tab name (e.g., "Section 1").
     */
    @Column(
            name = "title",
            nullable = false
    )
    @JsonProperty("title")
    private String title;

    /**
     * The display order of the section tab within the obligation check UI.
     */
    @Column(
            name = "display_order",
            nullable = false
    )
    @JsonProperty("display_order")
    private Integer displayOrder;

    /**
     * The timestamp when the section was created. Managed automatically.
     */
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    /**
     * The timestamp of the last update. Managed automatically.
     */
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "country_id",
            referencedColumnName = "id"
    )
    @JsonIgnore
    private Country country;

    @OneToMany(mappedBy = "obligationCheckSection")
    @JsonIgnore
    @JsonProperty("criteria")
    private List<Criteria> criteria = new ArrayList<>();

    /**
     * Sets the creation and update timestamps before the entity is persisted.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    /**
     * Sets the update timestamp before the entity is updated.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    @Transient
    @JsonProperty("country_id")
    public Integer getCountryId() {
        return (this.country != null) ? this.country.getId() : null;
    }
}