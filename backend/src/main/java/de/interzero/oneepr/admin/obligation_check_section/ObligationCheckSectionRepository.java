package de.interzero.oneepr.admin.obligation_check_section;

import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface ObligationCheckSectionRepository extends JpaRepository<ObligationCheckSection, Long> {

    /**
     * Finds all active sections for a given country ID.
     *
     * @param code The code of the country.
     * @return A list of active ObligationCheckSection entities.
     */
    List<ObligationCheckSection> findAllByCountry_Code(String code);


    Optional<ObligationCheckSection> findByDisplayOrderAndCountry_Code(Integer order,
                                                                       String code);
}
