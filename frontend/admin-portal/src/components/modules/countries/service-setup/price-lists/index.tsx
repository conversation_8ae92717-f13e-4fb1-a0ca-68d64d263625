"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { createCountryPriceList, deleteCountryPriceList } from "@/lib/api/country-price-lists";
import { getPriceLists } from "@/lib/api/price-lists";
import { getServiceSetupPriceLists } from "@/lib/api/service-setups";
import { queryClient } from "@/lib/react-query";
import { SetupPriceList } from "@/types/service-setup";
import { PriceList } from "@/types/service-setup/price-list";
import { formatCurrency } from "@/utils/format-currency";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { CheckCircle, Search } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useQuery } from "@tanstack/react-query";
import { AlertCircle } from "lucide-react";
import { enqueueSnackbar } from "notistack";
import { ChangeEvent, useEffect, useMemo, useRef, useState } from "react";
import { useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

interface ServiceSetupPriceListsProps {}

export function ServiceSetupPriceLists({}: ServiceSetupPriceListsProps) {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "price-lists";

  function handleOpenStep() {
    changeParam("step", "price-lists");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: priceLists, isFetching } = useQuery({
    queryKey: ["service-setup-price-lists", country.code],
    queryFn: () => getServiceSetupPriceLists(country.code),
  });

  const isComplete =
    priceLists &&
    !!priceLists.length &&
    priceLists.find((priceList) => priceList.price_list.condition_type_value === new Date().getFullYear().toString());

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="price-lists-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={handleOpenStep}
      id="price-lists-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">5. Select Prices</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
        {!isComplete && <AlertCircle className="size-6 fill-error stroke-white transition-all duration-300" />}
      </div>
      {isSelected && !isComplete && (
        <p className="text-error text-sm flex items-center gap-2">
          {!priceLists?.length && "There are no price lists registered. "}
          {!priceLists?.find(
            (priceList) => priceList.price_list.condition_type_value === new Date().getFullYear().toString()
          ) && "There is no price list for the current year"}
        </p>
      )}
      {isSelected && <CountryPriceListsForm priceLists={priceLists || []} onCloseStep={handleCloseStep} />}
    </div>
  );
}

const countryPriceListSchema = z.object({
  id: z.coerce
    .number()
    .transform((value) => Number(value) || undefined)
    .optional(),
  price_list_id: z.coerce.number().transform((value) => Number(value)),
  price_list: z.object({
    id: z.coerce.number(),
    name: z.string(),
    condition_type: z.string(),
    condition_type_value: z.string(),
    registration_fee: z.number().nullable(),
    variable_handling_fee: z.number().nullable(),
  }),
});

const countryPriceListsFormSchema = z
  .object({
    countryPriceLists: z.array(countryPriceListSchema).min(1, "You must add at least one price list"),
  })
  .superRefine((data, ctx) => {
    const currentYear = new Date().getFullYear();

    const currentYearPriceList = data.countryPriceLists.find(
      (priceList) => priceList.price_list.condition_type_value === currentYear.toString()
    );

    if (!currentYearPriceList) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ["countryPriceLists"],
        message: `You must add a price list for the current year (${currentYear})`,
      });
    }
  });

type CountryPriceListsFormData = z.infer<typeof countryPriceListsFormSchema>;

interface CountryPriceListsFormProps {
  priceLists: SetupPriceList[];
  onCloseStep: () => void;
}

function CountryPriceListsForm({ priceLists, onCloseStep }: CountryPriceListsFormProps) {
  const { country } = useServiceSetup();
  const {
    control,
    handleSubmit,
    formState: { isSubmitting, errors },
    setValue,
    reset,
  } = useForm<CountryPriceListsFormData>({
    resolver: zodResolver(countryPriceListsFormSchema),
    defaultValues: {
      countryPriceLists: priceLists.map((priceList) => ({
        ...priceList,
        price_list: {
          ...priceList.price_list,
          id: Number(priceList.price_list.id),
        },
      })),
    },
  });

  useEffect(() => {
    reset({
      countryPriceLists: priceLists.map((priceList) => ({
        ...priceList,
        price_list: {
          ...priceList.price_list,
          id: Number(priceList.price_list.id),
        },
      })),
    });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [priceLists]);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "countryPriceLists",
    keyName: "key",
  });

  async function handleFormSubmit(data: CountryPriceListsFormData) {
    try {
      if (!data.countryPriceLists.length) return;

      const promises = data.countryPriceLists
        .filter((p) => !p.id)
        .map(async (countryPriceList) => {
          return createCountryPriceList({ ...countryPriceList, country_id: country.id });
        });

      if (!promises.length) {
        enqueueSnackbar("No new price list was added", { variant: "warning" });
        return;
      }

      const responses = await Promise.allSettled(promises);

      responses.forEach((response, index) => {
        if (response.status !== "fulfilled") return;

        setValue(`countryPriceLists.${index}.id`, response.value.id);
      });

      queryClient.invalidateQueries({ queryKey: ["service-setup-price-lists", country.code] });

      enqueueSnackbar("Price lists saved successfully", { variant: "success" });
      onCloseStep();
    } catch (error) {
      enqueueSnackbar("Error saving price lists", { variant: "error" });
    }
  }

  async function handleDeleteCountryPriceList(index: number) {
    const countryPriceList = fields[index];

    if (!countryPriceList.id) {
      remove(index);
      return;
    }

    try {
      await deleteCountryPriceList(countryPriceList.id);

      remove(index);

      queryClient.invalidateQueries({ queryKey: ["service-setup-price-lists", country.code] });
      queryClient.invalidateQueries({ queryKey: ["service-setup-status", country.code] });
      enqueueSnackbar("Price list deleted successfully", { variant: "success" });
    } catch {
      enqueueSnackbar("Error deleting price list", { variant: "error" });
    }
  }

  function handleAddPriceList(priceList: PriceList) {
    if (fields.some((field) => Number(field.price_list_id) === Number(priceList.id))) return;
    append({ price_list_id: Number(priceList.id), price_list: { ...priceList, id: Number(priceList.id) } });
  }

  return (
    <div className="space-y-6 mt-6">
      <h3 className="text-primary font-bold">Lizenzero Price Lists</h3>
      <PriceListSearch
        onSelectPriceList={handleAddPriceList}
        selectedPriceListIds={fields.map((field) => field.price_list_id)}
      />
      <form onSubmit={handleSubmit(handleFormSubmit)} className="w-full pl-10 space-y-6">
        <div className="w-full space-y-6">
          {fields.map((field, index) => (
            <div key={field.price_list_id} className="flex items-start gap-4">
              <div className="flex flex-col gap-2 min-w-32">
                <p className="text-primary text-nowrap">Price List</p>
                <span className="text-tonal-dark-cream-30">{field.price_list.name}</span>
              </div>
              <div className="flex flex-col gap-2 min-w-32">
                <p className="text-primary text-nowrap">License Year</p>
                <span className="text-tonal-dark-cream-30">{field.price_list.condition_type_value || "-"}</span>
              </div>
              <div className="flex flex-col gap-2 min-w-32">
                <p className="text-primary text-nowrap">Registration Fee</p>
                <span className="text-tonal-dark-cream-30">
                  {formatCurrency(field.price_list.registration_fee || 0)}
                </span>
              </div>
              <div className="flex flex-col gap-2 min-w-32">
                <p className="text-primary text-nowrap">Variable Handling Fee</p>
                <span className="text-tonal-dark-cream-30">{field.price_list.variable_handling_fee || 0}%</span>
              </div>
              <div className="flex flex-col gap-2 min-w-32">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <button
                      type="button"
                      className="text-nowrap text-sm font-bold text-error hover:bg-error/30 rounded-full py-1 px-3"
                    >
                      Remove price list
                    </button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete service?</AlertDialogTitle>
                      <AlertDialogDescription>
                        By clicking on ”confirm” you are deleting this package and all content within it.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Back</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleDeleteCountryPriceList(index)}>Confirm</AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          ))}
        </div>
        <div className="space-y-6">
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
          {errors.countryPriceLists?.message && (
            <p className="text-error text-sm">{errors.countryPriceLists?.message}</p>
          )}
          <div className="flex items-center justify-end gap-10">
            <Button type="button" onClick={() => onCloseStep()} variant="text" color="dark-blue" size="medium">
              Cancel
            </Button>
            <Button
              type="submit"
              variant="filled"
              color={Object.keys(errors).length > 0 ? "red" : "dark-blue"}
              size="medium"
              className="w-60"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );
}

interface PriceListSearchProps {
  onSelectPriceList: (priceList: PriceList) => void;
  selectedPriceListIds: number[];
}

export function PriceListSearch({ onSelectPriceList, selectedPriceListIds }: PriceListSearchProps) {
  const [search, setSearch] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const debounceRef = useRef<NodeJS.Timeout | null>(null);

  function handleChange(e: ChangeEvent<HTMLInputElement>) {
    const newSearch = e.target.value;
    // setIsOpen(false);

    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
      debounceRef.current = null;
    }

    debounceRef.current = setTimeout(() => {
      refetch();
      setIsOpen(true);
    }, 500);

    setSearch(newSearch);
  }

  function handleSelectPriceList(priceList: PriceList) {
    if (selectedPriceListIds.includes(priceList.id)) return;

    setSearch("");
    setIsOpen(false);
    onSelectPriceList(priceList);
  }

  const {
    data: priceLists,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["price-lists", { search, service_type: "EU_LICENSE" }],
    queryFn: () => getPriceLists({ search, service_type: "EU_LICENSE" }),
    enabled: false,
  });

  const filteredPriceLists = useMemo(() => {
    return (priceLists || []).filter(
      (priceList) =>
        !selectedPriceListIds.includes(priceList.id) && priceList.name.toLowerCase().includes(search.toLowerCase())
    );
  }, [priceLists, search, selectedPriceListIds]);

  return (
    <div className="relative w-full lg:w-1/2 pl-10">
      <div className="relative w-full">
        <Input
          label="Add a price list by searching for it's name"
          placeholder="Search for a price list"
          leadingIcon={<Search />}
          value={search}
          onChange={handleChange}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setIsOpen(false)}
        />
        {isOpen && !!search && (
          <div className="absolute top-full mt-2 left-0 w-full max-h-[300px] overflow-y-auto bg-background py-3 rounded-2xl shadow-elevation-04-1">
            {isLoading && <div className="text-primary px-4 py-5">Loading...</div>}
            {!isLoading && !filteredPriceLists.length && (
              <div className="text-primary px-4 py-5">No price lists found</div>
            )}
            {!!filteredPriceLists.length &&
              filteredPriceLists.map((priceList) => (
                <div
                  key={priceList.id}
                  className="text-primary px-4 py-5 hover:bg-surface-01 cursor-pointer focus:bg-secondary/30"
                  onMouseDown={() => handleSelectPriceList(priceList)}
                >
                  {priceList.name}
                </div>
              ))}
          </div>
        )}
      </div>
    </div>
  );
}
