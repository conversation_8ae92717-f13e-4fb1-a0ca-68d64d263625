package de.interzero.oneepr.common.service;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Common AWS S3 service that provides centralized functionality for all S3 operations.
 * This service consolidates AWS S3 operations that were previously duplicated across
 * multiple services (UploadFilesService, FileService, UploadDataService).
 *
 * Features:
 * - Generate presigned URLs for file uploads
 * - Upload files directly to S3
 * - Download files from S3
 * - Delete files from S3
 * - List objects in S3 bucket
 * - Check if object exists in S3
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Getter
public class AwsService {

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;

    @Value("${aws.s3.bucket-name}")
    private String bucketName;

    /**
     * Response DTO for presigned URL requests
     */
    @Setter
    @Getter
    public static class PresignedUrlResponse {
        private String preSignedUrl;
        private Map<String, String> fields;

        public PresignedUrlResponse(String preSignedUrl, Map<String, String> fields) {
            this.preSignedUrl = preSignedUrl;
            this.fields = fields != null ? fields : new HashMap<>();
        }

    }

    /**
     * Response DTO for file download operations
     */
    @Getter
    public static class FileDownloadResponse {
        private final byte[] content;
        private final String contentType;
        private final long contentLength;

        public FileDownloadResponse(byte[] content, String contentType, long contentLength) {
            this.content = content;
            this.contentType = contentType;
            this.contentLength = contentLength;
        }

    }

    /**
     * Generate a presigned URL for uploading files to S3
     *
     * @param key         The S3 object key (file path)
     * @param contentType The content type of the file
     * @param duration    The duration for which the URL should be valid (default: 15 minutes)
     * @return PresignedUrlResponse containing the upload URL and any additional fields
     */
    public PresignedUrlResponse generatePresignedUploadUrl(String key, String contentType, Duration duration) {
        try {
            Duration urlDuration = duration != null ? duration : Duration.ofMinutes(15);

            PutObjectPresignRequest presignRequest = PutObjectPresignRequest.builder()
                    .signatureDuration(urlDuration)
                    .putObjectRequest(b -> b.bucket(bucketName).key(key).contentType(contentType))
                    .build();

            PresignedPutObjectRequest presignedRequest = s3Presigner.presignPutObject(presignRequest);

            log.info("Generated presigned URL for key: {} with duration: {} minutes", key, urlDuration.toMinutes());
            return new PresignedUrlResponse(presignedRequest.url().toString(), new HashMap<>());
        } catch (Exception e) {
            log.error("Error generating presigned URL for key: {}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to generate presigned URL: " + e.getMessage());
        }
    }

    /**
     * Generate a presigned URL for getting files to S3
     *
     * @param key         The S3 object key (file path)
     * @param duration    The duration for which the URL should be valid (default: 15 minutes)
     * @return PresignedUrlResponse containing the upload URL and any additional fields
     */
    public PresignedUrlResponse generatePresignedDownloadUrl(String key, Duration duration) {
        try {
            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(duration)
                    .getObjectRequest(b -> b.bucket(bucketName).key(key))
                    .build();

            PresignedGetObjectRequest presignedRequest = s3Presigner.presignGetObject(presignRequest);

            log.info("Generated presigned get URL for key: {} with duration: {} minutes", key, duration.toMinutes());
            return new PresignedUrlResponse(presignedRequest.url().toString(), new HashMap<>());
        } catch (Exception e) {
            log.error("Error generating presigned get URL for key: {}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to generate presigned URL: " + e.getMessage());
        }
    }

    /**
     * Generate a presigned URL with default 15-minute duration
     */
    public PresignedUrlResponse generatePresignedUploadUrl(String key, String contentType) {
        return generatePresignedUploadUrl(key, contentType, null);
    }

    /**
     * Upload a file directly to S3
     *
     * @param key         The S3 object key (file path)
     * @param file        The MultipartFile to upload
     * @param contentType The content type (optional, will use file's content type if null)
     * @return The S3 object key of the uploaded file
     */
    public String uploadFile(String key, MultipartFile file, String contentType) {
        try {
            String actualContentType = contentType != null ? contentType : file.getContentType();

            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(actualContentType)
                    .contentLength(file.getSize())
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));

            log.info("Successfully uploaded file to S3: bucket={}, key={}, size={} bytes", bucketName, key, file.getSize());
            return key;
        } catch (IOException e) {
            log.error("IO error uploading file to S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to read file content: " + e.getMessage());
        } catch (Exception e) {
            log.error("Error uploading file to S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to upload file to S3: " + e.getMessage());
        }
    }

    /**
     * Upload file content from byte array
     */
    public String uploadFile(String key, byte[] content, String contentType) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength((long) content.length)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromBytes(content));

            log.info("Successfully uploaded byte array to S3: bucket={}, key={}, size={} bytes", bucketName, key, content.length);
            return key;
        } catch (Exception e) {
            log.error("Error uploading byte array to S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to upload content to S3: " + e.getMessage());
        }
    }

    /**
     * Upload file content from InputStream
     */
    public String uploadFile(String key, InputStream inputStream, long contentLength, String contentType) {
        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .contentType(contentType)
                    .contentLength(contentLength)
                    .build();

            s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, contentLength));

            log.info("Successfully uploaded stream to S3: bucket={}, key={}, size={} bytes", bucketName, key, contentLength);
            return key;
        } catch (Exception e) {
            log.error("Error uploading stream to S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to upload stream to S3: " + e.getMessage());
        }
    }

    /**
     * Download a file from S3
     *
     * @param key The S3 object key (file path)
     * @return FileDownloadResponse containing the file content and metadata
     */
    public FileDownloadResponse downloadFile(String key) {
        try {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            ResponseInputStream<GetObjectResponse> responseResponseInputStream = s3Client.getObject(getObjectRequest);
            GetObjectResponse response = responseResponseInputStream.response();
            byte[] content = responseResponseInputStream.readAllBytes();

            log.info("Successfully downloaded file from S3: bucket={}, key={}, size={} bytes", bucketName, key, content.length);
            return new FileDownloadResponse(content, response.contentType(), response.contentLength());
        } catch (NoSuchKeyException e) {
            log.error("File not found in S3: bucket={}, key={}", bucketName, key);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found in S3: " + key);
        } catch (Exception e) {
            log.error("Error downloading file from S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to download file from S3: " + e.getMessage());
        }
    }

    /**
     * Download file content as byte array
     */
    public byte[] downloadFileContent(String key) {
        return downloadFile(key).getContent();
    }

    /**
     * Check if an object exists in S3
     *
     * @param key The S3 object key (file path)
     * @return true if the object exists, false otherwise
     */
    public boolean objectExists(String key) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.headObject(headObjectRequest);
            return true;
        } catch (NoSuchKeyException e) {
            return false;
        } catch (Exception e) {
            log.error("Error checking if object exists in S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to check object existence: " + e.getMessage());
        }
    }

    /**
     * Delete an object from S3
     *
     * @param key The S3 object key (file path)
     */
    public void deleteFile(String key) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            s3Client.deleteObject(deleteObjectRequest);
            log.info("Successfully deleted file from S3: bucket={}, key={}", bucketName, key);
        } catch (Exception e) {
            log.error("Error deleting file from S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to delete file from S3: " + e.getMessage());
        }
    }

    /**
     * Get object metadata without downloading the content
     *
     * @param key The S3 object key (file path)
     * @return HeadObjectResponse containing object metadata
     */
    public HeadObjectResponse getObjectMetadata(String key) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(key)
                    .build();

            return s3Client.headObject(headObjectRequest);
        } catch (NoSuchKeyException e) {
            log.error("File not found in S3: bucket={}, key={}", bucketName, key);
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found in S3: " + key);
        } catch (Exception e) {
            log.error("Error getting object metadata from S3: key={}", key, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to get object metadata: " + e.getMessage());
        }
    }

    /**
     * Copy an object within S3
     *
     * @param sourceKey      The source S3 object key
     * @param destinationKey The destination S3 object key
     */
    public void copyObject(String sourceKey, String destinationKey) {
        try {
            CopyObjectRequest copyObjectRequest = CopyObjectRequest.builder()
                    .sourceBucket(bucketName)
                    .sourceKey(sourceKey)
                    .destinationBucket(bucketName)
                    .destinationKey(destinationKey)
                    .build();

            s3Client.copyObject(copyObjectRequest);
            log.info("Successfully copied object in S3: from {} to {}", sourceKey, destinationKey);
        } catch (Exception e) {
            log.error("Error copying object in S3: from {} to {}", sourceKey, destinationKey, e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Failed to copy object: " + e.getMessage());
        }
    }
}
