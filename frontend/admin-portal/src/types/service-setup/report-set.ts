import { UploadedFile } from "../file";
import { ReportSetColumn } from "./report-set-column";
import { ReportSetFraction } from "./report-set-fraction";
import { ReportSetPriceList } from "./report-set-price-list";

export interface ReportSet {
  id: number;
  name: string;
  mode: ReportSetMode;
  type: ReportSetType;

  sheet_file?: UploadedFile | null;
  sheet_file_id: string | null;
  sheet_file_description: string | null;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  has_criteria?: boolean;

  packaging_service_id: number;
  columns: ReportSetColumn[];
  fractions: ReportSetFraction[];
}

export const MockReportSets: ReportSet[] = [];

export type ReportSetType = "FRACTIONS" | "CATEGORIES";

export enum ReportSetMode {
  ON_PLATAFORM = "ON_PLATAFORM",
  BY_EXCEL = "BY_EXCEL",
  NO_REPORTING = "NO_REPORTING",
  SALES_PACKAGING = "SALES_PACKAGING",
}
export type CreateReportSet = Omit<
  ReportSet,
  "id" | "created_at" | "updated_at" | "deleted_at" | "fractions" | "columns"
>;

export type UpdateReportSet = {
  name?: string;
  fractions?: Partial<ReportSetFraction>[];
  columns?: Partial<ReportSetColumn>[];
  price_lists?: Partial<ReportSetPriceList>[];
  sheet_file_id?: string | null;
  sheet_file_description?: string | null;
};
