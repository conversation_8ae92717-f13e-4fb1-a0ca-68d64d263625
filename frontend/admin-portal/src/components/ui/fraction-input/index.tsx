import { cn } from "@/lib/utils";
import React, { ComponentProps, forwardRef } from "react";
import { NumericFormat } from "react-number-format";

interface FractionInputProps extends Omit<ComponentProps<"input">, "type" | "onChange"> {
  label?: string;
  type: "currency" | "weight" | "fraction-currency";
  value?: number;
  onChange?: (value: string | number) => void;
  defaultValue?: string;
  error?: boolean | string;
}

export const FractionInput = forwardRef<HTMLDivElement, FractionInputProps>(function FractionInput(
  {
    type,
    value,
    onChange,
    placeholder = type === "currency" ? "0,00 €" : type === "fraction-currency" ? "0,000 €" : "0.00 kg",
    className = "",
    label,
    error,
    ...props
  },
  ref
) {
  const handleValueChange = (values: { floatValue: number }) => {
    const { floatValue } = values;

    if (floatValue === undefined) {
      onChange?.(0);
      return;
    }

    if (type === "currency") {
      onChange?.(floatValue * 100);
      return;
    }

    onChange?.(floatValue * 1000);
  };

  const getDisplayValue = () => {
    if (value === undefined || String(value) === "" || value === null) {
      return "";
    }

    if (type === "currency") {
      return (value as number) / 100;
    }

    return (value as number) / 1000;
  };

  return (
    <div ref={ref} className="flex flex-col gap-2">
      {!!label && (
        <label htmlFor="description" className="text-primary text-base font-centra">
          {label}
        </label>
      )}
      <NumericFormat
        value={getDisplayValue()}
        onValueChange={(values) => handleValueChange(values as { floatValue: number })}
        thousandSeparator="."
        decimalSeparator=","
        allowNegative={false}
        decimalScale={type === "fraction-currency" ? 3 : 2}
        suffix={type === "currency" || type === "fraction-currency" ? " €" : ""}
        placeholder={placeholder}
        data-invalid={!!error}
        className={cn(
          `formatted-input ${type}-input ${className}`,
          "text-right bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80 block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary  data-[invalid=true]:border-error data-[invalid=true]:bg-tonal-red-90 focus:data-[invalid=true]:outline-error disabled:bg-tonal-dark-cream-100 disabled:cursor-not-allowed disabled:bg-tonal-dark-cream-90 disabled:text-tonal-dark-cream-60 disabled:border-tonal-dark-cream-80",
          className
        )}
        {...props}
      />
      {!!error && typeof error === "string" && (
        <div className="flex justify-start items-center mt-2.5 space-x-2 ">
          <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
            {error}
          </span>
        </div>
      )}
    </div>
  );
});
