package de.interzero.oneepr.common.config;

import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.deser.BeanDeserializerModifier;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.common.BaseDtoDeserializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Configuration class for customizing Jackson's ObjectMapper.
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // Configure to ignore unknown properties globally
        mapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // Register JavaTimeModule for handling Java 8 date/time types like Instant
        mapper.registerModule(new JavaTimeModule());

        // Configure to write dates as ISO-8601 strings instead of timestamps
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        SimpleModule module = new SimpleModule();
        module.setDeserializerModifier(new BeanDeserializerModifier() {
            @Override
            public JsonDeserializer<?> modifyDeserializer(DeserializationConfig config,
                                                          BeanDescription beanDesc,
                                                          JsonDeserializer<?> deserializer) {

                // Only apply to classes that extend BaseDto
                if (BaseDto.class.isAssignableFrom(beanDesc.getBeanClass())) {
                    return new BaseDtoDeserializer(beanDesc.getBeanClass());
                }
                return deserializer;
            }
        });

        mapper.registerModule(module);
        return mapper;
    }
}
