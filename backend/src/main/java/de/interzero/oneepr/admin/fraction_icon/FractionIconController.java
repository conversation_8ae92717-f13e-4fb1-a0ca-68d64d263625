package de.interzero.oneepr.admin.fraction_icon;

import de.interzero.oneepr.admin.fraction_icon.dto.CreateFractionIconDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing fraction icons.
 */
@RestController
@RequestMapping(Api.FRACTION_ICONS)
@Tag(name = "FractionIcon")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class FractionIconController {

    private final FractionIconService fractionIconService;

    /**
     * Creates a new fraction icon from a given file ID.
     *
     * @param data The DTO containing the file_id.
     * @return The newly created FractionIcon entity with a 201 Created status.
     */
    @PostMapping
    @Operation(summary = "Create a new fraction icon")
    @ApiResponse(
            responseCode = "201",
            description = "Fraction icon created successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid input data"
    )
    @ApiResponse(
            responseCode = "404",
            description = "File not found"
    )
    public ResponseEntity<FractionIcon> create(@RequestBody CreateFractionIconDto data) {
        FractionIcon createdIcon = fractionIconService.create(data);
        return new ResponseEntity<>(createdIcon, HttpStatus.CREATED);
    }

    /**
     * Retrieves all non-deleted fraction icons.
     *
     * @return A list of all active fraction icons.
     */
    @GetMapping
    @Operation(summary = "Get all fraction icons")
    @ApiResponse(
            responseCode = "200",
            description = "Fraction icons retrieved successfully"
    )
    public ResponseEntity<List<FractionIcon>> findAll() {
        List<FractionIcon> icons = fractionIconService.findAll();
        return ResponseEntity.ok(icons);
    }

    /**
     * Retrieves a specific fraction icon by its ID.
     *
     * @param id The ID of the fraction icon to retrieve.
     * @return The found FractionIcon entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get fraction icon by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Fraction icon retrieved successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Fraction icon not found"
    )
    public ResponseEntity<FractionIcon> findOne(@PathVariable String id) {
        FractionIcon icon = fractionIconService.findOne(Integer.valueOf(id));
        return ResponseEntity.ok(icon);
    }

    /**
     * Soft-deletes a fraction icon by its ID.
     *
     * @param id The ID of the fraction icon to remove.
     * @return The updated entity with the deletion date set.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete fraction icon by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Fraction icon deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Fraction icon not found"
    )
    public ResponseEntity<FractionIcon> remove(@PathVariable String id) {
        // This assumes the service method is changed to: public FractionIcon remove(Integer id)
        FractionIcon removedIcon = fractionIconService.remove(Integer.valueOf(id));
        return ResponseEntity.ok(removedIcon);
    }
}
