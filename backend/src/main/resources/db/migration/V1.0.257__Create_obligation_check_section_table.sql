-- V{YYYYMMDDHHMM}__Create_obligation_check_section_table.sql
-- This script creates the 'obligation_check_section' table to group obligation check questions.

-- Create the main table for obligation check sections.
CREATE TABLE public.obligation_check_section
(
    -- The primary key, auto-incrementing. Corresponds to @Id @GeneratedValue.
    id            BIGSERIAL PRIMARY KEY,

    -- The title of the section, e.g., "Section 1". Corresponds to @Column(name = "title").
    title         TEXT      NOT NULL,

    -- The display order for the section tab in the UI. Corresponds to @Column(name = "display_order").
    display_order INTEGER   NOT NULL,

    -- The foreign key linking to the 'country' table. Corresponds to @Column(name = "country_id").
    country_id    INTEGER   NOT NULL,

    -- The creation timestamp. Corresponds to @Column(name = "created_at").
    created_at    TIMESTAMPTZ NOT NULL,

    -- The last update timestamp. Corresponds to @Column(name = "updated_at").
    updated_at    TIMESTAMPTZ NOT NULL,

    -- Define the foreign key constraint to ensure referential integrity with the 'country' table.
    CONSTRAINT fk_obligation_check_section_country
        FOREIGN KEY (country_id)
            REFERENCES public.country (id)
            ON DELETE RESTRICT -- Prevents deleting a country if it still has sections.
);

-- Add a comment to describe the table's purpose in the database schema.
COMMENT ON TABLE public.obligation_check_section IS 'Stores sections or tabs used to group questions in a country''s obligation check.';

-- Create an index on the foreign key column 'country_id' to improve the performance of queries
-- that filter or join by country.
CREATE INDEX idx_obligation_check_section_country_id ON public.obligation_check_section (country_id);


-- V{YYYYMMDDHHMM}__Add_section_id_to_criteria_table.sql
-- This script adds a nullable foreign key column to the 'criteria' table to link
-- each criterion (question) to an 'obligation_check_section'.

-- Add the new column to the 'criteria' table.
-- It is nullable because a criterion might not belong to any specific section.
-- The data type is BIGINT to match the 'id' column of the 'obligation_check_section' table (BIGSERIAL).
ALTER TABLE public.criteria
    ADD COLUMN obligation_check_section_id BIGINT;

-- Add the foreign key constraint to link 'criteria' to 'obligation_check_section'.
-- ON DELETE SET NULL means that if a section is deleted, the 'obligation_check_section_id'
-- for any criteria linked to it will be set to NULL, rather than deleting the criteria.
ALTER TABLE public.criteria
    ADD CONSTRAINT fk_criteria_obligation_check_section
        FOREIGN KEY (obligation_check_section_id)
            REFERENCES public.obligation_check_section (id)
            ON DELETE SET NULL;

-- Add a comment to describe the new column's purpose.
COMMENT ON COLUMN public.criteria.obligation_check_section_id IS 'Foreign key to the obligation_check_section table, grouping this criterion within a section.';

-- Create an index on the new foreign key column to improve query performance
-- when finding all criteria for a given section.
CREATE INDEX idx_criteria_obligation_check_section_id ON public.criteria (obligation_check_section_id);