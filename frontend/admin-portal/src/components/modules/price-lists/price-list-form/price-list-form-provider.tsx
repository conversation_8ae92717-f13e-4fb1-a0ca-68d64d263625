"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { create<PERSON>rice<PERSON>ist, getPriceList, updatePriceList } from "@/lib/api/price-lists";
import { queryClient } from "@/lib/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { enqueueSnackbar } from "notistack";
import { useEffect } from "react";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { GERMANY_THRESHOLDS } from "./price-list-form-fractions/germany";

export const PRICE_LIST_TYPES = [
  { label: "EU License", value: "EU_LICENSE" },
  { label: "Direct License", value: "DIRECT_LICENSE" },
  { label: "Action Guide", value: "ACTION_GUIDE" },
  { label: "Workshop", value: "WORKSHOP" },
] as const;

const PRICE_LIST_TYPE_VALUES = Object.values(PRICE_LIST_TYPES).map((type) => type.value);

export const NEAR_YEARS = [
  new Date().getFullYear() - 4,
  new Date().getFullYear() - 3,
  new Date().getFullYear() - 2,
  new Date().getFullYear() - 1,
  new Date().getFullYear(),
  new Date().getFullYear() + 1,
  new Date().getFullYear() + 2,
  new Date().getFullYear() + 3,
  new Date().getFullYear() + 4,
];

export const priceListFormSchema = z
  .object({
    form_type: z.enum(["CREATE", "UPDATE"]),
    id: z.number().optional(),
    type: z.enum([PRICE_LIST_TYPE_VALUES[0], ...PRICE_LIST_TYPE_VALUES.slice(0)], {
      required_error: "Service type is required",
    }),
    name: z.string().min(1, {
      message: "Name is required",
    }),
    description: z.string().min(1, {
      message: "Description is required",
    }),
    license_year: z.string().min(1, {
      message: "License year is required",
    }),
    start_date: z.string().min(1, {
      message: "Start date is required",
    }),
    end_date: z.string().min(1, {
      message: "End date is required",
    }),
    basic_price: z
      .number({
        required_error: "Basic price is required",
      })
      .min(1, { message: "Basic price must be greater than 0" })
      .optional(),
    minimum_price: z
      .number({
        required_error: "Minimum price is required",
      })
      .min(1, { message: "Minimum price must be greater than 0" })
      .optional(),
    registration_fee: z
      .number({
        required_error: "Registration fee is required",
      })
      .min(1, { message: "Registration fee must be greater than 0" })
      .optional(),
    handling_fee: z
      .number({
        required_error: "Handling fee is required",
      })
      .min(1, { message: "Handling fee must be greater than 0" })
      .optional(),
    variable_handling_fee: z
      .number({
        required_error: "Variable handling fee is required",
      })
      .min(1, { message: "Variable handling fee must be greater than 0%" })
      .max(100, { message: "Variable handling fee must be less than 100%" })
      .optional(),
    price: z
      .number({
        required_error: "Price is required",
      })
      .min(1, { message: "Price must be greater than 0" })
      .optional(),
    thresholds: z
      .array(
        z.object({
          title: z.string().min(1, { message: "Title is required" }),
          value: z.number(),
          helper_text: z.string().nullable().default(null),
          fractions: z.record(
            z.string(),
            z.object({
              code: z.string(),
              name: z.string(),
              value: z.number(),
            })
          ),
        })
      )
      .optional(),
  })
  .superRefine((data, ctx) => {
    if (data.type === "EU_LICENSE") {
      if (data.registration_fee === undefined) {
        ctx.addIssue({ code: "custom", message: "Registration fee is required", path: ["registration_fee"] });
      }
      if (data.handling_fee === undefined) {
        ctx.addIssue({ code: "custom", message: "Handling fee is required", path: ["handling_fee"] });
      }
      if (data.variable_handling_fee === undefined) {
        ctx.addIssue({ code: "custom", message: "Variable handling fee is required", path: ["variable_handling_fee"] });
      }
      return;
    }

    if (data.type === "DIRECT_LICENSE") {
      if (data.basic_price === undefined) {
        ctx.addIssue({ code: "custom", message: "Basic price is required", path: ["basic_price"] });
      }
      if (data.minimum_price === undefined) {
        ctx.addIssue({ code: "custom", message: "Minimum price is required", path: ["minimum_price"] });
      }

      if (data.thresholds === undefined) {
        ctx.addIssue({ code: "custom", message: "Thresholds are required", path: ["thresholds"] });
      } else {
        data.thresholds.forEach((threshold, thresholdIndex) => {
          if (thresholdIndex !== 0 && threshold.value <= 0) {
            ctx.addIssue({
              code: "custom",
              message: "Threshold must be greater than 0",
              path: ["thresholds", threshold.title, "value"],
            });
          }
        });
      }

      return;
    }

    if (data.type === "ACTION_GUIDE" || data.type === "WORKSHOP") {
      if (data.price === undefined) {
        ctx.addIssue({ code: "custom", message: "Price is required", path: ["price"] });
      }
    }
  });

export type PriceListFormData = z.infer<typeof priceListFormSchema>;

interface PriceListFormProviderProps {
  type: "CREATE" | "UPDATE";
  children: React.ReactNode;
  priceListId?: number;
}

export function PriceListFormProvider({ type, children, priceListId }: PriceListFormProviderProps) {
  const router = useRouter();

  const methods = useForm<PriceListFormData>({
    resolver: zodResolver(priceListFormSchema),
    defaultValues: {
      form_type: type,
      type: PRICE_LIST_TYPE_VALUES[0],
      license_year: String(NEAR_YEARS[4]),
      id: priceListId,
      thresholds: type === "CREATE" ? GERMANY_THRESHOLDS : undefined,
    },
  });

  const { data: priceList, isLoading: isLoadingPriceList } = useQuery({
    queryKey: ["price-list", priceListId],
    queryFn: () => getPriceList(priceListId!),
    enabled: type === "UPDATE" && priceListId !== undefined,
  });

  const { mutate: create } = useMutation({
    mutationKey: ["create-price-list"],
    mutationFn: (data: PriceListFormData) =>
      createPriceList({
        name: data.name,
        type: data.type,
        description: data.description,
        condition_type: "LICENSE_YEAR",
        condition_type_value: String(data.license_year),
        start_date: data.start_date,
        end_date: data.end_date,
        // EU LICENSE
        registration_fee: (data.type === "EU_LICENSE" && data.registration_fee) || null,
        handling_fee: (data.type === "EU_LICENSE" && data.handling_fee) || null,
        variable_handling_fee: (data.type === "EU_LICENSE" && data.variable_handling_fee) || null,
        // DIRECT LICENSE
        basic_price: (data.type === "DIRECT_LICENSE" && data.basic_price) || null,
        minimum_price: (data.type === "DIRECT_LICENSE" && data.minimum_price) || null,
        thresholds: (data.type === "DIRECT_LICENSE" && data.thresholds) || null,
        // ACTION GUIDE & WORKSHOP
        price: ((data.type === "ACTION_GUIDE" || data.type === "WORKSHOP") && data.price) || null,
      }),
  });

  const { mutate: update } = useMutation({
    mutationKey: ["update-price-list"],
    mutationFn: (data: PriceListFormData) =>
      updatePriceList(priceListId!, {
        name: data.name,
        type: data.type,
        description: data.description,
        condition_type: "LICENSE_YEAR",
        condition_type_value: String(data.license_year),
        start_date: new Date(data.start_date).toISOString(),
        end_date: new Date(data.end_date).toISOString(),
        // EU LICENSE
        registration_fee: (data.type === "EU_LICENSE" && data.registration_fee) || null,
        handling_fee: (data.type === "EU_LICENSE" && data.handling_fee) || null,
        variable_handling_fee: (data.type === "EU_LICENSE" && data.variable_handling_fee) || null,
        // DIRECT LICENSE
        basic_price: (data.type === "DIRECT_LICENSE" && data.basic_price) || null,
        minimum_price: (data.type === "DIRECT_LICENSE" && data.minimum_price) || null,
        thresholds: (data.type === "DIRECT_LICENSE" && data.thresholds) || null,
        // ACTION GUIDE & WORKSHOP
        price: ((data.type === "ACTION_GUIDE" || data.type === "WORKSHOP") && data.price) || null,
      }),
  });

  function handleFormSubmit(data: PriceListFormData) {
    if (type === "UPDATE") {
      update(data, {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["price-lists"] });

          enqueueSnackbar("Price list updated successfully", { variant: "success" });
        },
        onError: () => {
          enqueueSnackbar("Failed to update price list", { variant: "error" });
        },
      });
      return;
    }

    create(data, {
      onSuccess: (createdPriceList) => {
        queryClient.invalidateQueries({ queryKey: ["price-lists"] });

        enqueueSnackbar("Price list created successfully", { variant: "success" });
        router.push(
          `/price-lists?service_type=${createdPriceList.type}&license_year=${createdPriceList.condition_type_value}`
        );
      },
      onError: () => {
        enqueueSnackbar("Failed to create price list", { variant: "error" });
      },
    });
  }

  const serviceType = useWatch({
    control: methods.control,
    name: "type",
  });

  useEffect(() => {
    if (!priceList) return;

    methods.reset({
      ...priceList,
      form_type: type,
      type: priceList?.type,
      license_year: String(priceList?.condition_type_value),
      start_date: String(
        priceList?.start_date ? new Date(priceList.start_date).toISOString().split("T")[0] : undefined
      ),
      end_date: String(priceList?.end_date ? new Date(priceList.end_date).toISOString().split("T")[0] : undefined),
      price: priceList?.price || undefined,
      basic_price: priceList?.basic_price || undefined,
      minimum_price: priceList?.minimum_price || undefined,
      registration_fee: priceList?.registration_fee || undefined,
      handling_fee: priceList?.handling_fee || undefined,
      variable_handling_fee: priceList?.variable_handling_fee || undefined,
      thresholds: serviceType === "DIRECT_LICENSE" ? priceList?.thresholds || GERMANY_THRESHOLDS : undefined,
    });

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [priceList]);

  useEffect(() => {
    if (serviceType === "EU_LICENSE") {
      methods.setValue("basic_price", undefined);
      methods.setValue("minimum_price", undefined);
      methods.setValue("thresholds", undefined);
      methods.setValue("price", undefined);
    }

    if (serviceType === "DIRECT_LICENSE") {
      methods.setValue("thresholds", priceList?.thresholds || GERMANY_THRESHOLDS);
      methods.setValue("price", undefined);
      methods.setValue("registration_fee", undefined);
      methods.setValue("handling_fee", undefined);
      methods.setValue("variable_handling_fee", undefined);
    }

    if (serviceType === "ACTION_GUIDE" || serviceType === "WORKSHOP") {
      methods.setValue("basic_price", undefined);
      methods.setValue("minimum_price", undefined);
      methods.setValue("thresholds", undefined);
      methods.setValue("registration_fee", undefined);
      methods.setValue("handling_fee", undefined);
      methods.setValue("variable_handling_fee", undefined);
    }

    methods.clearErrors([
      "price",
      "basic_price",
      "minimum_price",
      "registration_fee",
      "handling_fee",
      "variable_handling_fee",
      "thresholds",
    ]);

    // TODO: check if it is possible to remove this eslint-disable comment
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [serviceType]);

  if (isLoadingPriceList) return <PriceListSkeleton />;

  return (
    <FormProvider {...methods}>
      <form id="price-list-form" className="w-full space-y-10" onSubmit={methods.handleSubmit(handleFormSubmit)}>
        {children}
      </form>
    </FormProvider>
  );
}

function PriceListSkeleton() {
  return (
    <div className="w-full space-y-10">
      <div className="col-span-3 bg-white rounded-3xl py-9 px-8">
        <div className="mb-6">
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="w-full space-y-6">
          <div className="w-full">
            <Skeleton className="h-[72px] w-full" />
          </div>
          <div className="w-full">
            <Skeleton className="h-[72px] w-full" />
          </div>
          <div className="w-full">
            <Skeleton className="h-[144px] w-full" />
          </div>
          <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
          </div>
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
          <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
            <div className="col-span-2">
              <Skeleton className="h-[72px] w-full" />
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center justify-end gap-10">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-60" />
      </div>
    </div>
  );
}
