package de.interzero.oneepr.admin.obligation_check_section;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.obligation_check_section.dto.CreateObligationCheckSectionDto;
import de.interzero.oneepr.admin.obligation_check_section.dto.UpdateObligationCheckSectionDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Unit tests for the {@link ObligationCheckSectionController}.
 * This class validates the controller's request mapping, security, and interaction with the mocked service layer.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ObligationCheckSectionControllerTest {


    @Autowired
    private CountryRepository countryRepository;

    private static final String API_BASE_URL = Api.OBLIGATION_CHECK_SECTIONS;

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private ObligationCheckSectionService obligationCheckSectionService;

    private ObligationCheckSection mockSection;

    private Country testCountry;

    /**
     * Sets up a mock ObligationCheckSection object to be used as a consistent return value
     * from the mocked service layer in the tests.
     */
    @BeforeEach
    void setUp() {
        testCountry = createAndSaveTestCountry();
        mockSection = new ObligationCheckSection();
        mockSection.setId(1L);
        mockSection.setTitle("Test Section 1");
        mockSection.setDisplayOrder(1);
        mockSection.setCreatedAt(Instant.now());
        mockSection.setUpdatedAt(Instant.now());
        mockSection.setCountry(testCountry);
    }

    /**
     * Verifies that a POST request to {@link ObligationCheckSectionController#create(CreateObligationCheckSectionDto)}
     * successfully creates a new section.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldReturnCreatedSection() throws Exception {
        // Arrange
        Country country = createAndSaveTestCountry();
        CreateObligationCheckSectionDto createDto = new CreateObligationCheckSectionDto();
        createDto.setTitle("Test Section 1");
        createDto.setDisplayOrder(1);
        createDto.setCountryCode(country.getCode());
        when(obligationCheckSectionService.create(any(CreateObligationCheckSectionDto.class))).thenReturn(mockSection);

        // Act & Assert
        mockMvc.perform(post(API_BASE_URL).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id", is(1)))
                .andExpect(jsonPath("$.title", is("Test Section 1")));
    }

    /**
     * Verifies that a GET request to {@link ObligationCheckSectionController#findAllByCountry(String)}
     * successfully retrieves a list of sections for a given country code.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAllByCountry_shouldReturnListOfSections() throws Exception {
        // Arrange
        when(obligationCheckSectionService.getOrSetupSectionsForCountry(anyString())).thenReturn(List.of(mockSection));

        // Act & Assert
        mockMvc.perform(get(API_BASE_URL + "/country/DE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(1)))
                .andExpect(jsonPath("$[0].country_id", is(testCountry.getId())));
    }

    /**
     * Verifies that a GET request to {@link ObligationCheckSectionController#findOne(Long)}
     * successfully retrieves a single section by its ID.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnSingleSection() throws Exception {
        // Arrange
        when(obligationCheckSectionService.findOne(anyLong())).thenReturn(mockSection);

        // Act & Assert
        mockMvc.perform(get(API_BASE_URL + "/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(1)))
                .andExpect(jsonPath("$.title", is("Test Section 1")));
    }

    /**
     * Verifies that a GET request to {@link ObligationCheckSectionController#findOne(Long)}
     * returns a 404 Not Found status when the service throws a ResponseStatusException.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnNotFound_whenServiceThrowsException() throws Exception {
        // Arrange
        when(obligationCheckSectionService.findOne(99L)).thenThrow(new ResponseStatusException(
                HttpStatus.NOT_FOUND,
                                                                                               "Section not found"));

        // Act & Assert
        mockMvc.perform(get(API_BASE_URL + "/99")).andExpect(status().isNotFound());
    }

    /**
     * Verifies that a PATCH request to {@link ObligationCheckSectionController#update(long, UpdateObligationCheckSectionDto)}
     * successfully updates a section.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldReturnUpdatedSection() throws Exception {
        // Arrange
        UpdateObligationCheckSectionDto updateDto = new UpdateObligationCheckSectionDto();
        updateDto.setTitle("Updated Title");

        ObligationCheckSection updatedSection = new ObligationCheckSection();
        updatedSection.setId(1L);
        updatedSection.setTitle("Updated Title");
        updatedSection.setDisplayOrder(1);

        when(obligationCheckSectionService.update(anyLong(), any(UpdateObligationCheckSectionDto.class))).thenReturn(
                updatedSection);

        // Act & Assert
        mockMvc.perform(put(API_BASE_URL + "/1").contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(1)))
                .andExpect(jsonPath("$.title", is("Updated Title")));
    }

    /**
     * Verifies that a DELETE request to {@link ObligationCheckSectionController#delete(long)}
     * successfully deletes a section and returns a 204 No Content status.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void delete_shouldReturnNoContent() throws Exception {
        // Arrange
        doNothing().when(obligationCheckSectionService).delete(anyLong());

        // Act & Assert
        mockMvc.perform(delete(API_BASE_URL + "/remove/1")).andExpect(status().isNoContent());
    }

    private Country createAndSaveTestCountry() {
        Country country = countryRepository.findByCode("DE").orElse(new Country());
        country.setName("Germany");
        country.setCode("DE");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        country.setUpdatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }
}