import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { getServiceSetupReportSet } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";
import { AlertCircle, Loader2 } from "lucide-react";
import { ReportTable } from "../../report-table";

interface PreviewReportTableDialogProps {
  countryCode: string;
  reportSetId: number;
  children: React.ReactNode;
}

export function PreviewReportTableDialog({ countryCode, reportSetId, children }: PreviewReportTableDialogProps) {
  const { data: reportSet, isLoading } = useQuery({
    queryKey: ["service-setup-report-set", countryCode, reportSetId],
    queryFn: () => getServiceSetupReportSet(countryCode, reportSetId),
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-6xl">
        <DialogHeader>
          <DialogTitle>Preview Report Table (SaaS)</DialogTitle>
          <DialogDescription>Check how the report table will be set for the user to answer</DialogDescription>
        </DialogHeader>
        <div className="bg-background rounded-4xl p-8 flex flex-col gap-5">
          <p className="text-primary text-2xl font-bold">{reportSet?.packaging_service.name}</p>
          {!isLoading && (
            <span className="text-sm text-tonal-dark-cream-30">
              Please provide the estimate quantity so we are able to estimate the future costs
            </span>
          )}
          {isLoading && (
            <div className="flex items-center justify-center gap-2">
              <Loader2 className="size-5 text-primary animate-spin" />
              <p className="text-md text-tonal-dark-cream-30">Loading report table...</p>
            </div>
          )}
          {!isLoading && reportSet?.fractions.length && (
            <ReportTable fractions={reportSet.fractions} columns={reportSet.columns} />
          )}
          {!isLoading && !reportSet?.fractions.length && (
            <div className="py-4 flex items-center gap-2">
              <AlertCircle className="size-5 text-error stroke-white" />
              <p className="text-error  text-sm">No fractions registered yet!</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
