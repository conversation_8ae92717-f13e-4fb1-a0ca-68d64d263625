package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import de.interzero.oneepr.common.service.AwsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.time.Duration;

@Configuration
public class FractionIconImageUrlSerializer extends StdSerializer<String> {

    private transient AwsService awsService;

    public FractionIconImageUrlSerializer() {
        super(String.class);
    }

    @Autowired
    public FractionIconImageUrlSerializer(AwsService awsService) {
        super(String.class);
        this.awsService = awsService;
    }

    @Override
    public void serialize(String value,
                          JsonGenerator jsonGenerator,
                          SerializerProvider serializerProvider) throws IOException {
        String preSignedUrl = awsService.generatePresignedDownloadUrl(value, Duration.ofMinutes(15)).getPreSignedUrl();
        jsonGenerator.writeString(preSignedUrl);

    }
}
