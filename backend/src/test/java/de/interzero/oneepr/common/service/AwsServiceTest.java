package de.interzero.oneepr.common.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.server.ResponseStatusException;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for AwsService
 */
@ExtendWith(MockitoExtension.class)
class AwsServiceTest {

    @Mock
    private S3Client s3Client;

    @Mock
    private S3Presigner s3Presigner;

    @Mock
    private PresignedPutObjectRequest presignedPutObjectRequest;

    private AwsService awsService;

    private static final String TEST_BUCKET = "test-bucket";
    private static final String TEST_KEY = "test/file.txt";
    private static final String TEST_CONTENT_TYPE = "text/plain";

    @BeforeEach
    void setUp() {
        awsService = new AwsService(s3Client, s3Presigner);
        ReflectionTestUtils.setField(awsService, "bucketName", TEST_BUCKET);
    }

    @Test
    void generatePresignedUploadUrl_Success() throws Exception {
        // Arrange
        URL testUrl = new URL("https://test-bucket.s3.amazonaws.com/test/file.txt");
        when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
                .thenReturn(presignedPutObjectRequest);
        when(presignedPutObjectRequest.url()).thenReturn(testUrl);

        // Act
        AwsService.PresignedUrlResponse response = awsService.generatePresignedUploadUrl(TEST_KEY, TEST_CONTENT_TYPE);

        // Assert
        assertNotNull(response);
        assertEquals(testUrl.toString(), response.getPreSignedUrl());
        assertNotNull(response.getFields());
        assertTrue(response.getFields().isEmpty());

        verify(s3Presigner).presignPutObject(any(PutObjectPresignRequest.class));
    }

    @Test
    void generatePresignedUploadUrl_WithCustomDuration_Success() throws Exception {
        // Arrange
        URL testUrl = new URL("https://test-bucket.s3.amazonaws.com/test/file.txt");
        Duration customDuration = Duration.ofMinutes(30);
        when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
                .thenReturn(presignedPutObjectRequest);
        when(presignedPutObjectRequest.url()).thenReturn(testUrl);

        // Act
        AwsService.PresignedUrlResponse response = awsService.generatePresignedUploadUrl(TEST_KEY, TEST_CONTENT_TYPE, customDuration);

        // Assert
        assertNotNull(response);
        assertEquals(testUrl.toString(), response.getPreSignedUrl());

        verify(s3Presigner).presignPutObject(any(PutObjectPresignRequest.class));
    }

    @Test
    void generatePresignedUploadUrl_Exception_ThrowsResponseStatusException() {
        // Arrange
        when(s3Presigner.presignPutObject(any(PutObjectPresignRequest.class)))
                .thenThrow(new RuntimeException("AWS error"));

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.generatePresignedUploadUrl(TEST_KEY, TEST_CONTENT_TYPE));

        assertTrue(exception.getReason().contains("Failed to generate presigned URL"));
    }

    @Test
    void uploadFile_MultipartFile_Success() throws IOException {
        // Arrange
        MockMultipartFile mockFile = new MockMultipartFile(
                "file", "test.txt", TEST_CONTENT_TYPE, "test content".getBytes());
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(PutObjectResponse.builder().build());

        // Act
        String result = awsService.uploadFile(TEST_KEY, mockFile, TEST_CONTENT_TYPE);

        // Assert
        assertEquals(TEST_KEY, result);
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }

    @Test
    void uploadFile_MultipartFile_IOError_ThrowsResponseStatusException() throws IOException {
        // Arrange
        MockMultipartFile mockFile = mock(MockMultipartFile.class);
        when(mockFile.getInputStream()).thenThrow(new IOException("IO error"));
        when(mockFile.getSize()).thenReturn(100L);

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.uploadFile(TEST_KEY, mockFile, TEST_CONTENT_TYPE));

        assertTrue(exception.getReason().contains("Failed to read file content"));
    }

    @Test
    void uploadFile_ByteArray_Success() {
        // Arrange
        byte[] content = "test content".getBytes();
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(PutObjectResponse.builder().build());

        // Act
        String result = awsService.uploadFile(TEST_KEY, content, TEST_CONTENT_TYPE);

        // Assert
        assertEquals(TEST_KEY, result);
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }

    @Test
    void uploadFile_InputStream_Success() {
        // Arrange
        byte[] content = "test content".getBytes();
        ByteArrayInputStream inputStream = new ByteArrayInputStream(content);
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenReturn(PutObjectResponse.builder().build());

        // Act
        String result = awsService.uploadFile(TEST_KEY, inputStream, content.length, TEST_CONTENT_TYPE);

        // Assert
        assertEquals(TEST_KEY, result);
        verify(s3Client).putObject(any(PutObjectRequest.class), any(RequestBody.class));
    }

    @Test
    void uploadFile_S3Exception_ThrowsResponseStatusException() {
        // Arrange
        byte[] content = "test content".getBytes();
        when(s3Client.putObject(any(PutObjectRequest.class), any(RequestBody.class)))
                .thenThrow(S3Exception.builder().message("S3 error").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.uploadFile(TEST_KEY, content, TEST_CONTENT_TYPE));

        assertTrue(exception.getReason().contains("Failed to upload content to S3"));
    }

    @Test
    void downloadFile_Success() throws IOException {
        // Arrange
        byte[] expectedContent = "test content".getBytes();
        GetObjectResponse getObjectResponse = GetObjectResponse.builder()
                .contentType(TEST_CONTENT_TYPE)
                .contentLength((long) expectedContent.length)
                .build();

        ResponseInputStream<GetObjectResponse> responseInputStream = 
                new ResponseInputStream<>(getObjectResponse, new ByteArrayInputStream(expectedContent));

        when(s3Client.getObject(any(GetObjectRequest.class))).thenReturn(responseInputStream);

        // Act
        AwsService.FileDownloadResponse response = awsService.downloadFile(TEST_KEY);

        // Assert
        assertNotNull(response);
        assertArrayEquals(expectedContent, response.getContent());
        assertEquals(TEST_CONTENT_TYPE, response.getContentType());
        assertEquals(expectedContent.length, response.getContentLength());

        verify(s3Client).getObject(any(GetObjectRequest.class));
    }

    @Test
    void downloadFile_NoSuchKey_ThrowsResponseStatusException() {
        // Arrange
        when(s3Client.getObject(any(GetObjectRequest.class)))
                .thenThrow(NoSuchKeyException.builder().message("Key not found").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.downloadFile(TEST_KEY));

        assertTrue(exception.getReason().contains("File not found in S3"));
    }

    @Test
    void downloadFileContent_Success() throws IOException {
        // Arrange
        byte[] expectedContent = "test content".getBytes();
        GetObjectResponse getObjectResponse = GetObjectResponse.builder()
                .contentType(TEST_CONTENT_TYPE)
                .contentLength((long) expectedContent.length)
                .build();

        ResponseInputStream<GetObjectResponse> responseInputStream = 
                new ResponseInputStream<>(getObjectResponse, new ByteArrayInputStream(expectedContent));

        when(s3Client.getObject(any(GetObjectRequest.class))).thenReturn(responseInputStream);

        // Act
        byte[] result = awsService.downloadFileContent(TEST_KEY);

        // Assert
        assertArrayEquals(expectedContent, result);
    }

    @Test
    void objectExists_True() {
        // Arrange
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenReturn(HeadObjectResponse.builder().build());

        // Act
        boolean exists = awsService.objectExists(TEST_KEY);

        // Assert
        assertTrue(exists);
        verify(s3Client).headObject(any(HeadObjectRequest.class));
    }

    @Test
    void objectExists_False() {
        // Arrange
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(NoSuchKeyException.builder().message("Key not found").build());

        // Act
        boolean exists = awsService.objectExists(TEST_KEY);

        // Assert
        assertFalse(exists);
    }

    @Test
    void objectExists_OtherException_ThrowsResponseStatusException() {
        // Arrange
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("S3 error").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.objectExists(TEST_KEY));

        assertTrue(exception.getReason().contains("Failed to check object existence"));
    }

    @Test
    void deleteFile_Success() {
        // Arrange
        when(s3Client.deleteObject(any(DeleteObjectRequest.class)))
                .thenReturn(DeleteObjectResponse.builder().build());

        // Act
        assertDoesNotThrow(() -> awsService.deleteFile(TEST_KEY));

        // Assert
        verify(s3Client).deleteObject(any(DeleteObjectRequest.class));
    }

    @Test
    void deleteFile_Exception_ThrowsResponseStatusException() {
        // Arrange
        when(s3Client.deleteObject(any(DeleteObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("S3 error").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.deleteFile(TEST_KEY));

        assertTrue(exception.getReason().contains("Failed to delete file from S3"));
    }

    @Test
    void getObjectMetadata_Success() {
        // Arrange
        HeadObjectResponse expectedResponse = HeadObjectResponse.builder()
                .contentType(TEST_CONTENT_TYPE)
                .contentLength(100L)
                .build();
        when(s3Client.headObject(any(HeadObjectRequest.class))).thenReturn(expectedResponse);

        // Act
        HeadObjectResponse response = awsService.getObjectMetadata(TEST_KEY);

        // Assert
        assertEquals(expectedResponse, response);
        verify(s3Client).headObject(any(HeadObjectRequest.class));
    }

    @Test
    void getObjectMetadata_NoSuchKey_ThrowsResponseStatusException() {
        // Arrange
        when(s3Client.headObject(any(HeadObjectRequest.class)))
                .thenThrow(NoSuchKeyException.builder().message("Key not found").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.getObjectMetadata(TEST_KEY));

        assertTrue(exception.getReason().contains("File not found in S3"));
    }

    @Test
    void copyObject_Success() {
        // Arrange
        String destinationKey = "destination/file.txt";
        when(s3Client.copyObject(any(CopyObjectRequest.class)))
                .thenReturn(CopyObjectResponse.builder().build());

        // Act
        assertDoesNotThrow(() -> awsService.copyObject(TEST_KEY, destinationKey));

        // Assert
        verify(s3Client).copyObject(any(CopyObjectRequest.class));
    }

    @Test
    void copyObject_Exception_ThrowsResponseStatusException() {
        // Arrange
        String destinationKey = "destination/file.txt";
        when(s3Client.copyObject(any(CopyObjectRequest.class)))
                .thenThrow(S3Exception.builder().message("S3 error").build());

        // Act & Assert
        ResponseStatusException exception = assertThrows(ResponseStatusException.class,
                () -> awsService.copyObject(TEST_KEY, destinationKey));

        assertTrue(exception.getReason().contains("Failed to copy object"));
    }

    @Test
    void getBucketName_ReturnsConfiguredBucketName() {
        // Act
        String bucketName = awsService.getBucketName();

        // Assert
        assertEquals(TEST_BUCKET, bucketName);
    }
}
