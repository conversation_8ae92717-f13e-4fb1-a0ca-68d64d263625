import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { FractionIcon } from "@/components/ui/fraction-icon";
import { FractionInput } from "@/components/ui/fraction-input";
import { Skeleton } from "@/components/ui/skeleton";
import { getServiceSetupReportSet } from "@/lib/api/service-setups";
import { useQuery } from "@tanstack/react-query";
import { AlertCircle } from "lucide-react";

interface PreviewCalculatorDialogProps {
  countryCode: string;
  reportSetId: number;
  children: React.ReactNode;
}

export function PreviewCalculatorDialog({ countryCode, reportSetId, children }: PreviewCalculatorDialogProps) {
  const { data: reportSet, isLoading } = useQuery({
    queryKey: ["service-setup-report-set", countryCode, reportSetId],
    queryFn: () => getServiceSetupReportSet(countryCode, reportSetId),
  });

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="bg-surface-02 py-9 max-w-2xl">
        <DialogHeader>
          <DialogTitle>Preview Calculator (Shop)</DialogTitle>
          <DialogDescription>Check how the calculator will be set for the user to answer</DialogDescription>
        </DialogHeader>
        <div className="bg-surface-03 rounded-4xl p-8 flex flex-col gap-5">
          <p className="text-primary text-2xl font-bold">{reportSet?.packaging_service.name}</p>
          <span className="text-sm text-tonal-dark-cream-30">
            Please provide the estimate quantity so we are able to estimate the future costs
          </span>
          {isLoading && (
            <div className="rounded-[20px] bg-tonal-dark-cream-80 overflow-hidden space-y-[1px]">
              {isLoading &&
                [...Array(5)].map((_, index) => (
                  <div key={index} className="bg-background px-5 py-4 flex items-center gap-4">
                    <Skeleton className="w-5 h-5 rounded-full" />
                    <Skeleton className="w-8 h-8" />
                    <Skeleton className="w-24 h-6" />
                    <Skeleton className="w-20 h-8" />
                    <Skeleton className="w-6 h-6" />
                  </div>
                ))}
            </div>
          )}
          {reportSet && !!reportSet.fractions.length && (
            <div className="rounded-[20px] bg-tonal-dark-cream-80 overflow-hidden space-y-[1px]">
              {reportSet.fractions.map((fraction) => (
                <div key={fraction.id} className="bg-background px-5 py-4 flex items-center gap-4">
                  <span className="w-5 h-5 bg-secondary rounded-full"></span>
                  <FractionIcon size="medium" iconUrl={fraction.fraction_icon.image_url} />
                  <p className="text-primary font-bold flex-1">{fraction.name}</p>
                  <FractionInput type="weight" />
                  <span className="text-primary">kg</span>
                </div>
              ))}
            </div>
          )}
          {!isLoading && !reportSet?.fractions.length && (
            <div className="py-4 flex items-center gap-2">
              <AlertCircle className="size-5 text-error stroke-white" />
              <p className="text-error  text-sm">No fractions registered yet!</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
