package de.interzero.oneepr.admin.upload_files;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.shared.dto.CustomHeadersDto;
import de.interzero.oneepr.admin.upload_files.dto.CreateFileDto;
import de.interzero.oneepr.admin.upload_files.dto.LambdaPresignedResponseDto;
import de.interzero.oneepr.admin.upload_files.dto.RequestPresignedUrlDto;
import de.interzero.oneepr.common.service.AwsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.security.SecureRandom;
import java.time.Instant;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class UploadFilesService {

    private final CountryRepository countryRepository;

    private final FilesRepository filesRepository;

    private final AwsService awsService;


    /**
     * Request presigned url using AWS SDK
     *
     * @param requestPresignedUrlDto The found RequestPresignedUrlDto entity.
     * @return The found Object entity.
     */
    public LambdaPresignedResponseDto requestUrl(RequestPresignedUrlDto requestPresignedUrlDto) {
        try {
            String filename = requestPresignedUrlDto.getFilename();
            String fileType = requestPresignedUrlDto.getFileType();

            // Use the common AwsService to generate presigned URL
            AwsService.PresignedUrlResponse presignedResponse = awsService.generatePresignedUploadUrl(filename, fileType);

            // Create response DTO
            LambdaPresignedResponseDto response = new LambdaPresignedResponseDto();
            response.setUploadUrl(presignedResponse.getPreSignedUrl());
            response.setFields(new HashMap<>(presignedResponse.getFields()));

            return response;
        } catch (Exception e) {
            log.error("Error generating presigned URL", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Problems generating url");
        }
    }

    /**
     * upload file to s3 and database
     *
     * @param data             The found CreateFileDto entity.
     * @param customHeadersDto The found CustomHeadersDto entity.
     * @param file             upload file,MultipartFile type
     * @return The found Files entity.
     * @ts-legacy the files entity seems to do not have the primary key, so I use the UUID here
     */
    @Transactional(timeout = 20)
    public Files uploadFile(CreateFileDto data,
                            CustomHeadersDto customHeadersDto,
                            MultipartFile file) {
        String userId = customHeadersDto.getUserId();
        String userRole = customHeadersDto.getUserRole();

        long size = file.getSize();
        String originalname = file.getOriginalFilename();
        String mimetype = file.getContentType();

        if (data.getCountryId() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid country ID");
        }
        // Note: Country validation would need Country repository
        Optional<Country> country = countryRepository.findById(data.getCountryId());
        if (country.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found");
        }

        String filename = generateFilename(data.getDocumentType(), originalname);

        try {
            // Upload file using the common AwsService
            awsService.uploadFile(filename, file, mimetype);
            log.info("Successfully uploaded file {} to S3 bucket {}", filename, awsService.getBucketName());
        } catch (Exception e) {
            log.error("Error uploading file to S3", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error uploading file to S3");
        }

        // Create file record in database
        Files fileEntity = new Files();
        fileEntity.setId(UUID.randomUUID().toString());
        fileEntity.setUserId(userId);
        fileEntity.setCreatorType(userRole);
        fileEntity.setDocumentType(String.valueOf(data.getDocumentType()));
        fileEntity.setCountryId(data.getCountryId());
        fileEntity.setSize(String.valueOf(size));
        fileEntity.setName(filename);
        fileEntity.setExtension(mimetype);
        fileEntity.setOriginalName(originalname);
        fileEntity.setCreatedAt(Instant.now());
        return filesRepository.save(fileEntity);
    }


    /**
     * get file from s3 using AWS SDK
     *
     * @param fileId file id
     * @return result map
     */
    public Map<String, Object> getFile(String fileId) {
        Optional<Files> fileOptional = filesRepository.findById(fileId);
        if (fileOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "File not found");
        }

        Files file = fileOptional.get();

        try {
            // Download file using the common AwsService
            AwsService.FileDownloadResponse downloadResponse = awsService.downloadFile(file.getName());

            Map<String, Object> result = new HashMap<>();
            result.put("file", file);
            result.put("buffer", downloadResponse.getContent());

            log.info("Successfully downloaded file {} from S3 bucket {}", file.getName(), awsService.getBucketName());
            return result;
        } catch (ResponseStatusException e) {
            // Re-throw ResponseStatusException as-is
            throw e;
        } catch (Exception e) {
            log.error("Error downloading file from S3", e);
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Error downloading file from S3");
        }
    }

    /**
     * generate filename
     *
     * @param docType  document type
     * @param filename file name
     * @return result filename
     */
    public String generateFilename(CreateFileDto.DocumentType docType,
                                   String filename) {
        LocalDate today = LocalDate.now();
        int year = today.getYear();
        String month = String.format("%02d", today.getMonthValue());
        String day = String.format("%02d", today.getDayOfMonth());
        String hash = generateHash();
        return docType + "/" + year + "/" + month + "/" + day + "/" + hash + "-" + filename;
    }

    /**
     * generate hash length 8 0-9 A-Z
     *
     * @return result hash string
     */
    public String generateHash() {
        SecureRandom random = new SecureRandom();
        byte[] randomBytes = new byte[8];
        random.nextBytes(randomBytes);
        StringBuilder hash = new StringBuilder();
        for (byte b : randomBytes) {
            int value = Math.abs(b) % 36;
            if (value < 10) {
                hash.append((char) ('0' + value));
            } else {
                hash.append((char) ('A' + value - 10));
            }
        }
        return hash.substring(0, 8);
    }
}
