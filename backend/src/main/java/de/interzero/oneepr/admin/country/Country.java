package de.interzero.oneepr.admin.country;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.entity.CountryFollower;
import de.interzero.oneepr.admin.other_cost.OtherCost;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import de.interzero.oneepr.admin.obligation_check_section.ObligationCheckSection;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "country",
        schema = "public"
)
public class Country {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @NotNull
    @Column(
            name = "authorize_representative_obligated",
            nullable = false
    )
    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated = false;

    @NotNull
    @Column(
            name = "code",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("code")
    private String code;

    @NotNull
    @Column(
            name = "flag_url",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("flag_url")
    private String flagUrl;

    @NotNull
    @Column(
            name = "other_costs_obligated",
            nullable = false
    )
    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated = false;

    @NotNull
    @Column(
            name = "is_published",
            nullable = false
    )
    @JsonProperty("is_published")
    private Boolean isPublished = false;

    // --- Relationships ---

    @OneToMany(mappedBy = "country")
    @JsonIgnore
    @JsonProperty("followers")
    private List<CountryFollower> followers = new ArrayList<>();

    @OneToMany(mappedBy = "country")
    @JsonIgnore
    @JsonProperty("country_price_lists")
    private List<CountryPriceList> countryPriceLists = new ArrayList<>();

    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("criterias")
    private List<Criteria> criterias = new ArrayList<>();

    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("other_costs")
    private List<OtherCost> otherCosts = new ArrayList<>();

    @OneToMany(
            mappedBy = "country",
            fetch = FetchType.EAGER
    )
    @JsonProperty("packaging_services")
    private List<PackagingService> packagingServices = new ArrayList<>();

    @OneToMany(mappedBy = "country")
    @JsonProperty("representative_tiers")
    private List<RepresentativeTier> representativeTiers = new ArrayList<>();

    @OneToMany(mappedBy = "country")
    @JsonIgnore
    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    /**
     * A list of obligation check sections associated with this country.
     * This represents the inverse side of the many-to-one relationship
     * from ObligationCheckSection to Country.
     */
    @OneToMany(mappedBy = "country")
    @JsonIgnore
    @JsonProperty("obligation_check_sections")
    private List<ObligationCheckSection> obligationCheckSections = new ArrayList<>();

}